import { getToken } from 'next-auth/jwt';
import {
  NextFetchEvent,
  NextMiddleware,
  NextRequest,
  NextResponse,
} from 'next/server';
import { isPublicPath } from './helpers';

export function verifyValidToken(middleware: NextMiddleware) {
  return async (req: NextRequest, event: NextFetchEvent) => {
    const token = await getToken({ req, secret: process.env.NEXTAUTH_SECRET });

    // Skip token verification for public paths
    const ispublic = isPublicPath(req.nextUrl.pathname);

    // If no token, redirect to auth (this middleware runs first)
    const callBackUrl = `${req.nextUrl.pathname}${req.nextUrl.search}`;

    if (!token) {
      if (!ispublic) {
        return NextResponse.redirect(
          new URL(
            `/auth?callbackUrl=${encodeURIComponent(callBackUrl)}`,
            req.nextUrl,
          ),
        );
      } else {
        return NextResponse.next();
      }
    }

    const isValidToken = await getUserInfo(token.accessToken);
    if (!isValidToken) {
      const response = NextResponse.redirect(
        new URL(
          `/auth?callbackUrl=${encodeURIComponent(callBackUrl)}&expired=true`,
          req.nextUrl,
        ),
      );
      response.cookies.delete('next-auth.session-token');
      response.cookies.delete('__Secure-next-auth.session-token');
      response.cookies.delete('next-auth.csrf-token');
      response.cookies.delete('__Secure-next-auth.csrf-token');
      response.cookies.delete('next-auth.callback-url');
      response.cookies.delete('__Secure-next-auth.callback-url');
      return response;
    }

    return middleware(req, event);
  };
}

async function getUserInfo(accessToken: string) {
  const result = await fetch(
    `${process.env.NEXT_PUBLIC_BASEURL_PROD || process.env.NEXT_PUBLIC_BASEURL || ''}/api/user/users/user-info`,
    {
      method: 'GET',
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
    },
  );

  return result.ok;
}
